# PowerShell script for testing AutoGen Studio Chat API
# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
# Set input/output encoding to UTF-8 for proper Chinese character handling
$OutputEncoding = [System.Text.Encoding]::UTF8
[System.Console]::InputEncoding = [System.Text.Encoding]::UTF8
[System.Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AutoGen Studio Chat API 测试脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置全局变量
$global:API_KEY = "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"
$global:OPENAI_BASE_URL = "https://gnomic.nengyongai.cn/v1"
$BASE_URL = "http://localhost:1113"

Write-Host "[INFO] 开始测试 AutoGen Studio Chat API..." -ForegroundColor Green
Write-Host "[INFO] 基础URL: $BASE_URL" -ForegroundColor Green
Write-Host "[INFO] API密钥: $global:API_KEY" -ForegroundColor Green
Write-Host "[INFO] OpenAI基础URL: $global:OPENAI_BASE_URL" -ForegroundColor Green
Write-Host ""

# 测试1: 根端点 - 获取API基本信息
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试1: 获取API基本信息 (GET /)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/" -Method GET -ContentType "application/json" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试2: 健康检查
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试2: 健康检查 (GET /health)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/health" -Method GET -ContentType "application/json" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试3: 非流式聊天 - 基本对话
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试3: 非流式聊天 - 基本对话 (POST /chat)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body3 = @{
    messages = @(
        @{
            type = "text"
            content = "你好，请介绍一下你自己"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body3)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试4: 非流式聊天 - 机器学习问题
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试4: 非流式聊天 - 机器学习问题" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body4 = @{
    messages = @(
        @{
            type = "text"
            content = "解释一下机器学习的基本概念"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body4)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试5: 非流式聊天 - 带API密钥
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试5: 非流式聊天 - 带API密钥" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body5 = @{
    messages = @(
        @{
            type = "text"
            content = "写一个Python函数来计算斐波那契数列"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body5)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试6: 非流式聊天 - Ollama模型
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试6: 非流式聊天 - Ollama模型" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body6 = @{
    messages = @(
        @{
            type = "text"
            content = "你好世界"
            role = "user"
        }
    )
    model_name = "qwen3:latest"
    model_type = "ollama"
    openai_base_url = "http://localhost:11434"
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body6)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试7: 流式聊天 - 基本对话
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试7: 流式聊天 - 基本对话 (POST /chat/stream)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "[INFO] 注意: 流式响应将显示Server-Sent Events格式" -ForegroundColor Blue
$body7 = @{
    messages = @(
        @{
            type = "text"
            content = "写一首关于春天的诗"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $true
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body7)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat/stream" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试8: 流式聊天 - 讲笑话
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试8: 流式聊天 - 讲笑话" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body8 = @{
    messages = @(
        @{
            type = "text"
            content = "讲个笑话"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $true
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body8)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat/stream" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试9: 多轮对话测试
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试9: 多轮对话测试" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body9 = @{
    messages = @(
        @{
            type = "text"
            content = "我想学习编程"
            role = "user"
        },
        @{
            type = "text"
            content = "很好！编程是一项很有用的技能。你想学习哪种编程语言呢？"
            role = "assistant"
        },
        @{
            type = "text"
            content = "我想学Python"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body9)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

测试10: 带MCP服务器URL的请求
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试10: 带MCP服务器URL的请求" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body10 = @{
    messages = @(
        @{
            type = "text"
            content = "测试MCP功能"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    mcp_url_list = @("http://localhost:1111/mcp")
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body10)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试11: 带团队ID的请求
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试11: 带团队ID的请求" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body11 = @{
    messages = @(
        @{
            type = "text"
            content = "团队协作测试"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    team_id = 6
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body11)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试12: 错误测试 - 无效的模型类型
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试12: 错误测试 - 无效的模型类型" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body12 = @{
    messages = @(
        @{
            type = "text"
            content = "测试错误处理"
            role = "user"
        }
    )
    model_name = "gpt-3.5-turbo"
    model_type = "invalid_type"
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body12)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试13: 错误测试 - 缺少必需参数
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试13: 错误测试 - 缺少必需参数" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body13 = @{
    messages = @(
        @{
            type = "text"
            content = "测试缺少参数"
            role = "user"
        }
    )
} | ConvertTo-Json -Depth 10

try {
    # Convert JSON string to UTF-8 bytes to ensure proper encoding
    $bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body13)
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $bodyBytes -ContentType "application/json; charset=utf-8" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试14: 错误测试 - 无效的JSON格式
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试14: 错误测试 - 无效的JSON格式" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$invalidJson = "{invalid json}"

try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $invalidJson -ContentType "application/json" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

# 测试15: 图片消息测试
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "测试15: 图片消息测试" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
$body15 = @{
    messages = @(
        @{
            type = "image"
            content = "1_1752497070478.png"
            role = "user"
        },@{
            type = "text"
            content = "图片中讲述了什么内容"
            role = "user"
        }
    )
    model_name = "gpt-4o-mini"
    model_type = "openai"
    api_key = $global:API_KEY
    openai_base_url = $global:OPENAI_BASE_URL
    stream = $false
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/chat" -Method POST -Body $body15 -ContentType "application/json" -UseBasicParsing
    Write-Host $response.Content
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
}
Write-Host ""
Read-Host "按回车键继续..."

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "测试完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "[INFO] 所有测试用例已执行完毕" -ForegroundColor Green
Write-Host "[INFO] 请检查上述响应结果和状态码" -ForegroundColor Green
Write-Host ""
Write-Host "测试用例说明:" -ForegroundColor White
Write-Host "1-2:   基础功能测试 (根端点、健康检查)" -ForegroundColor White
Write-Host "3-6:   非流式聊天测试 (不同模型和参数)" -ForegroundColor White
Write-Host "7-8:   流式聊天测试" -ForegroundColor White
Write-Host "9:     多轮对话测试" -ForegroundColor White
Write-Host "10-11: 高级功能测试 (MCP、团队ID)" -ForegroundColor White
Write-Host "12-14: 错误处理测试" -ForegroundColor White
Write-Host "15:    图片消息测试" -ForegroundColor White
Write-Host ""
Write-Host "全局变量设置:" -ForegroundColor Magenta
Write-Host "API_KEY: $global:API_KEY" -ForegroundColor Magenta
Write-Host "OPENAI_BASE_URL: $global:OPENAI_BASE_URL" -ForegroundColor Magenta
Write-Host ""
Read-Host "按回车键退出..."
