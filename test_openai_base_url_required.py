#!/usr/bin/env python3
"""
测试脚本：验证 openai_base_url 参数是否为必选
"""

import requests
import json

def test_missing_openai_base_url():
    """测试缺少 openai_base_url 参数的情况"""
    url = "http://localhost:8000/chat"
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "测试缺少 openai_base_url 参数",
                "role": "user"
            }
        ],
        "model_name": "gpt-3.5-turbo",
        "model_type": "openai",
        "api_key": "test-api-key",
        "stream": False
        # 注意：这里故意不包含 openai_base_url 参数
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 422:
            print("✅ 测试通过：缺少 openai_base_url 参数时返回 422 错误")
            return True
        else:
            print("❌ 测试失败：应该返回 422 错误")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_with_openai_base_url():
    """测试包含 openai_base_url 参数的情况"""
    url = "http://localhost:8000/chat"
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "测试包含 openai_base_url 参数",
                "role": "user"
            }
        ],
        "model_name": "gpt-3.5-turbo",
        "model_type": "openai",
        "api_key": "test-api-key",
        "openai_base_url": "https://api.openai.com/v1",
        "stream": False
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code != 422:
            print("✅ 测试通过：包含 openai_base_url 参数时不返回 422 错误")
            return True
        else:
            print("❌ 测试失败：不应该返回 422 错误")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("测试 openai_base_url 参数是否为必选")
    print("=" * 50)
    
    print("\n1. 测试缺少 openai_base_url 参数的情况:")
    test1_result = test_missing_openai_base_url()
    
    print("\n2. 测试包含 openai_base_url 参数的情况:")
    test2_result = test_with_openai_base_url()
    
    print("\n" + "=" * 50)
    if test1_result and test2_result:
        print("🎉 所有测试通过！openai_base_url 参数已成功设置为必选")
    else:
        print("⚠️  部分测试失败，请检查配置")
    print("=" * 50)
