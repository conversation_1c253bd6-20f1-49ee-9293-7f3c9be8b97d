"""
FastAPI service wrapper for chat_backend function
Provides REST API endpoints for chat functionality
"""

import asyncio
import json
import logging
import traceback
import sys
import os
from typing import List, Optional, Dict, Any, Union
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
import uvicorn
try:
    from .chat_backend import chat_backend
except ImportError:
    from chat_backend import chat_backend

# Set UTF-8 encoding for the entire application
if sys.platform.startswith('win'):
    # Windows specific UTF-8 setup
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Pydantic models for request/response
class Message(BaseModel):
    type: str = Field(..., description="Message type: 'text' or 'image'")
    content: str = Field(..., description="Message content")
    role: str = Field(..., description="Message role: 'user', 'assistant', 'system'")


class ChatRequest(BaseModel):
    messages: List[Message] = Field(..., description="List of messages in the conversation")
    model_name: str = Field(..., description="Name of the model to use")
    model_type: str = Field(..., description="Type of model: 'ollama', 'openai'")
    api_key: Optional[str] = Field(None, description="API key for model authentication")
    openai_base_url: str = Field(..., description="Base URL for the model API")
    stream: bool = Field(True, description="Whether to stream the response")
    mcp_url_list: List[str] = Field(default=[], description="List of MCP server URLs")
    team_id: Optional[int] = Field(None, description="Team ID for team-based chat")


class ChatResponse(BaseModel):
    type: str = Field(..., description="Response type")
    content: str = Field(..., description="Response content")
    source: Optional[str] = Field(None, description="Response source")


# Initialize FastAPI app with proper encoding support
app = FastAPI(
    title="AutoGen Studio Chat API",
    description="FastAPI wrapper for AutoGen Studio chat backend",
    version="1.0.0"
)

# Add middleware to ensure proper UTF-8 handling
@app.middleware("http")
async def add_utf8_header(request: Request, call_next):
    """Middleware to ensure UTF-8 encoding in responses"""
    response = await call_next(request)
    if response.headers.get("content-type", "").startswith("application/json"):
        response.headers["content-type"] = "application/json; charset=utf-8"
    return response


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AutoGen Studio Chat API",
        "version": "1.0.0",
        "endpoints": {
            "chat": "/chat",
            "chat_stream": "/chat/stream",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "autogen-studio-chat-api"}


@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    """
    Non-streaming chat endpoint

    Args:
        request: ChatRequest containing messages and configuration

    Returns:
        ChatResponse with the model's response
    """
    try:
        logger.info(f"Received chat request: model_name={request.model_name}, model_type={request.model_type}")
        logger.debug(f"Request details: {request.model_dump()}")

        # Convert Pydantic messages to dict format expected by chat_backend
        messages_dict = [
            {
                "type": msg.type,
                "content": msg.content,
                "role": msg.role
            }
            for msg in request.messages
        ]

        logger.debug(f"Converted messages: {messages_dict}")

        # Call chat_backend with stream=False
        logger.info("Calling chat_backend...")
        result_generator = chat_backend(
            messages=messages_dict,
            model_name=request.model_name,
            model_type=request.model_type,
            api_key=request.api_key,
            openai_base_url=request.openai_base_url,
            stream=False,
            MCP_url_list=request.mcp_url_list,
            team_id=request.team_id
        )

        # Since chat_backend always returns a generator, we need to collect the results
        results = []
        async for item in result_generator:
            results.append(item)

        logger.debug(f"chat_backend results: {results}")

        # Handle the results - could be a list of messages or a single result
        if results:
            if len(results) == 1 and isinstance(results[0], list):
                # Team mode returns a list of messages - return the raw list as JSON
                import json

                def serialize_message(msg):
                    """Convert message object to JSON-serializable format"""
                    if isinstance(msg, dict):
                        serialized = {}
                        for key, value in msg.items():
                            if key == 'content':
                                # Handle content field specially
                                if isinstance(value, list):
                                    # For tool calls, convert to string representation
                                    serialized[key] = [str(item) for item in value]
                                else:
                                    serialized[key] = str(value) if value is not None else ""
                            elif key in ['created_at', 'models_usage']:
                                # Convert complex objects to string
                                serialized[key] = str(value) if value is not None else None
                            else:
                                serialized[key] = value
                        return serialized
                    else:
                        return str(msg)

                # Serialize all messages
                serialized_messages = [serialize_message(msg) for msg in results[0]]

                response = ChatResponse(
                    type="team_result",
                    content=json.dumps(serialized_messages, ensure_ascii=False, indent=2),
                    source="team"
                )
            elif isinstance(results[0], dict):
                # Single message result
                content = results[0].get("content", "")
                # Handle case where content might be a list (e.g., tool calls)
                if isinstance(content, list):
                    content_str = "\n".join(str(item) for item in content)
                else:
                    content_str = str(content)

                response = ChatResponse(
                    type=results[0].get("type", "result"),
                    content=content_str,
                    source=results[0].get("source")
                )
            else:
                # Fallback for other result types
                response = ChatResponse(
                    type="result",
                    content=str(results[0]),
                    source=None
                )
        else:
            response = ChatResponse(
                type="error",
                content="No response received from chat backend",
                source=None
            )

        logger.info("Chat request completed successfully")
        return response

    except Exception as e:
        logger.error(f"Chat processing failed: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")


@app.post("/chat/stream")
async def chat_stream_endpoint(request: ChatRequest):
    """
    Streaming chat endpoint
    
    Args:
        request: ChatRequest containing messages and configuration
        
    Returns:
        StreamingResponse with Server-Sent Events
    """
    try:
        # Convert Pydantic messages to dict format expected by chat_backend
        messages_dict = [
            {
                "type": msg.type,
                "content": msg.content,
                "role": msg.role
            }
            for msg in request.messages
        ]
        
        async def generate_stream():
            """Generator function for streaming response with proper UTF-8 encoding"""
            try:
                # Call chat_backend with stream=True
                async for chunk in chat_backend(
                    messages=messages_dict,
                    model_name=request.model_name,
                    model_type=request.model_type,
                    api_key=request.api_key,
                    openai_base_url=request.openai_base_url,
                    stream=True,
                    MCP_url_list=request.mcp_url_list,
                    team_id=request.team_id
                ):
                    # Format as Server-Sent Events with proper UTF-8 encoding
                    if isinstance(chunk, dict):
                        # Use safe JSON serialization for dict chunks with proper UTF-8 encoding
                        try:
                            data = json.dumps(chunk, default=str, ensure_ascii=False)
                            yield f"data: {data}\n\n"
                        except (TypeError, ValueError) as e:
                            # If serialization fails, convert to string representation
                            data = json.dumps({"type": "message", "content": str(chunk)}, ensure_ascii=False)
                            yield f"data: {data}\n\n"
                    else:
                        # Check if chunk has dump() method (autogen message object)
                        if hasattr(chunk, 'dump'):
                            try:
                                data = json.dumps(chunk.dump(), ensure_ascii=False)
                                yield f"data: {data}\n\n"
                            except (TypeError, ValueError) as e:
                                # Fallback to string representation
                                data = json.dumps({"type": "message", "content": str(chunk)}, ensure_ascii=False)
                                yield f"data: {data}\n\n"
                        else:
                            data = json.dumps({"type": "message", "content": str(chunk)}, ensure_ascii=False)
                            yield f"data: {data}\n\n"

                # Send end-of-stream marker
                yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                error_data = json.dumps({
                    "type": "error",
                    "content": f"Stream processing failed: {str(e)}"
                }, ensure_ascii=False)
                yield f"data: {error_data}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream; charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream; charset=utf-8"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Stream setup failed: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception on {request.method} {request.url}: {str(exc)}")
    logger.error(f"Full traceback: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc),
            "path": str(request.url),
            "traceback": traceback.format_exc().split('\n') if logger.level <= logging.DEBUG else None
        }
    )


def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """
    Run the FastAPI server

    Args:
        host: Host to bind to
        port: Port to bind to
        reload: Enable auto-reload for development
    """


    uvicorn.run(
        "fastapi_chat_service:app",
        host=host,
        port=port,
        reload=reload,
        log_level="debug",  # Changed to debug for more detailed logs
        access_log=True,
        # Ensure proper encoding handling
        loop="asyncio",
        http="httptools"
    )


if __name__ == "__main__":
    # Run the server
    run_server(host="0.0.0.0", port=1113, reload=True)
